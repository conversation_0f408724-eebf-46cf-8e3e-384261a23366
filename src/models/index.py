"""
索引数据模型

定义规范索引、语言索引和框架索引的数据结构
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class FrameworkIndex(BaseModel):
    """框架索引模型"""

    name: str = Field(..., description="框架名称")
    standards: List[str] = Field(default_factory=list, description="规范文件路径列表")
    rule_count: int = Field(default=0, description="规则总数")
    last_updated: datetime = Field(
        default_factory=datetime.now, description="最后更新时间"
    )
    version: Optional[str] = Field(None, description="框架版本")
    tags: List[str] = Field(default_factory=list, description="标签列表")


class LanguageIndex(BaseModel):
    """语言索引模型"""

    language: str = Field(..., description="编程语言名称")
    standards: List[str] = Field(
        default_factory=list, description="通用规范文件路径列表"
    )
    frameworks: Dict[str, FrameworkIndex] = Field(
        default_factory=dict, description="框架索引字典"
    )
    total_rules: int = Field(default=0, description="该语言的规则总数")
    last_updated: datetime = Field(
        default_factory=datetime.now, description="最后更新时间"
    )
    extensions: List[str] = Field(default_factory=list, description="文件扩展名列表")
    api_endpoints: Dict[str, str] = Field(
        default_factory=dict, description="API端点配置"
    )


class SearchIndexEntry(BaseModel):
    """搜索索引条目"""

    rule_id: str = Field(..., description="规则ID")
    section: str = Field(..., description="所属章节")
    title: str = Field(..., description="规则标题")
    relevance: float = Field(default=1.0, description="相关性分数")
    language: Optional[str] = Field(None, description="编程语言")
    framework: Optional[str] = Field(None, description="框架名称")


class StandardsIndex(BaseModel):
    """规范索引主模型"""

    metadata: Dict[str, Any] = Field(default_factory=dict, description="索引元数据")
    languages: Dict[str, LanguageIndex] = Field(
        default_factory=dict, description="语言索引字典"
    )
    search_index: Dict[str, Dict[str, List[SearchIndexEntry]]] = Field(
        default_factory=dict, description="搜索索引，按关键词和标签组织"
    )
    statistics: Dict[str, Any] = Field(default_factory=dict, description="统计信息")

    def __init__(self, **data):
        super().__init__(**data)
        if not self.metadata:
            self.metadata = {
                "version": "1.0.0",
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_standards": 0,
                "total_rules": 0,
                "supported_languages": [],
            }
        if not self.search_index:
            self.search_index = {"keywords": {}, "tags": {}}
        if not self.statistics:
            self.statistics = {
                "rules_by_severity": {"critical": 0, "high": 0, "medium": 0, "low": 0},
                "rules_by_language": {},
                "sections_by_language": {},
            }

    def add_language(self, language: str, language_index: LanguageIndex) -> None:
        """添加语言索引"""
        self.languages[language] = language_index
        self.metadata["supported_languages"] = list(self.languages.keys())
        self.metadata["last_updated"] = datetime.now().isoformat()

    def get_language_index(self, language: str) -> Optional[LanguageIndex]:
        """获取语言索引"""
        return self.languages.get(language)

    def add_framework(
        self, language: str, framework: str, framework_index: FrameworkIndex
    ) -> None:
        """为指定语言添加框架索引"""
        if language not in self.languages:
            self.languages[language] = LanguageIndex(language=language)

        self.languages[language].frameworks[framework] = framework_index
        self.metadata["last_updated"] = datetime.now().isoformat()

    def get_framework_index(
        self, language: str, framework: str
    ) -> Optional[FrameworkIndex]:
        """获取框架索引"""
        language_index = self.get_language_index(language)
        if language_index:
            return language_index.frameworks.get(framework)
        return None

    def add_search_entry(
        self, keyword: str, language: str, entry: SearchIndexEntry
    ) -> None:
        """添加搜索索引条目"""
        if "keywords" not in self.search_index:
            self.search_index["keywords"] = {}

        if keyword not in self.search_index["keywords"]:
            self.search_index["keywords"][keyword] = {}

        if language not in self.search_index["keywords"][keyword]:
            self.search_index["keywords"][keyword][language] = []

        self.search_index["keywords"][keyword][language].append(entry)

    def search_by_keyword(
        self, keyword: str, language: Optional[str] = None
    ) -> List[SearchIndexEntry]:
        """按关键词搜索"""
        results = []

        if "keywords" in self.search_index and keyword in self.search_index["keywords"]:
            keyword_entries = self.search_index["keywords"][keyword]

            if language:
                # 搜索特定语言
                if language in keyword_entries:
                    results.extend(keyword_entries[language])
            else:
                # 搜索所有语言
                for lang_entries in keyword_entries.values():
                    results.extend(lang_entries)

        # 按相关性排序
        results.sort(key=lambda x: x.relevance, reverse=True)
        return results

    def update_statistics(self) -> None:
        """更新统计信息"""
        total_rules = 0
        rules_by_language = {}
        sections_by_language = {}

        for language, lang_index in self.languages.items():
            lang_rules = lang_index.total_rules
            total_rules += lang_rules
            rules_by_language[language] = lang_rules

            # 计算章节数（这里简化处理，实际应该从规范文档中统计）
            sections_count = len(lang_index.frameworks) + 1  # 框架数 + 通用规范
            sections_by_language[language] = sections_count

        self.metadata["total_rules"] = total_rules
        self.metadata["total_standards"] = len(self.languages)

        self.statistics["rules_by_language"] = rules_by_language
        self.statistics["sections_by_language"] = sections_by_language
        self.metadata["last_updated"] = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "metadata": self.metadata,
            "languages": {
                lang: {
                    "language": idx.language,
                    "standards": idx.standards,
                    "frameworks": {
                        fw_name: {
                            "name": fw_idx.name,
                            "standards": fw_idx.standards,
                            "rule_count": fw_idx.rule_count,
                            "last_updated": fw_idx.last_updated.isoformat(),
                            "version": fw_idx.version,
                            "tags": fw_idx.tags,
                        }
                        for fw_name, fw_idx in idx.frameworks.items()
                    },
                    "total_rules": idx.total_rules,
                    "last_updated": idx.last_updated.isoformat(),
                    "extensions": idx.extensions,
                    "api_endpoints": idx.api_endpoints,
                }
                for lang, idx in self.languages.items()
            },
            "search_index": {
                "keywords": {
                    keyword: {
                        lang: [
                            {
                                "rule_id": entry.rule_id,
                                "section": entry.section,
                                "title": entry.title,
                                "relevance": entry.relevance,
                                "language": entry.language,
                                "framework": entry.framework,
                            }
                            for entry in entries
                        ]
                        for lang, entries in lang_entries.items()
                    }
                    for keyword, lang_entries in self.search_index.get(
                        "keywords", {}
                    ).items()
                },
                "tags": self.search_index.get("tags", {}),
            },
            "statistics": self.statistics,
        }
