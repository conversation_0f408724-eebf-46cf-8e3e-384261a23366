"""
代码合规性检查服务

实现代码规范合规性检查和违规解释功能
"""

import logging
import re
from typing import Any, Dict, List, Optional, Tuple

from ..config.settings import config
from ..models.standard import SeverityLevel, StandardDocument, StandardRule
from ..storage.file_manager import file_manager
from ..utils.parsers import parser
from ..utils.validators import validator

logger = logging.getLogger("CodeStandardMCP.ComplianceChecker")


class ComplianceViolation:
    """合规性违规记录"""

    def __init__(
        self,
        rule_id: str,
        rule_title: str,
        severity: SeverityLevel,
        line_number: int,
        column: int,
        message: str,
        code_snippet: str,
        suggestion: Optional[str] = None,
    ):
        self.rule_id = rule_id
        self.rule_title = rule_title
        self.severity = severity
        self.line_number = line_number
        self.column = column
        self.message = message
        self.code_snippet = code_snippet
        self.suggestion = suggestion

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "rule_id": self.rule_id,
            "rule_title": self.rule_title,
            "severity": self.severity.value,
            "line_number": self.line_number,
            "column": self.column,
            "message": self.message,
            "code_snippet": self.code_snippet,
            "suggestion": self.suggestion,
        }


class ComplianceChecker:
    """代码合规性检查服务类"""

    def __init__(self):
        self.max_code_length = config.max_code_length
        self.context_lines = config.context_lines

    async def check_compliance(
        self,
        code: str,
        language: str,
        framework: Optional[str] = None,
        rule_ids: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        检查代码合规性

        Args:
            code: 要检查的代码
            language: 编程语言
            framework: 可选的框架名称
            rule_ids: 可选的特定规则ID列表

        Returns:
            包含检查结果的字典
        """
        try:
            # 验证输入参数
            if not validator.validate_language(language):
                return {
                    "success": False,
                    "error": f"Unsupported language: {language}",
                    "error_type": "validation_error",
                }

            is_valid_code, code_errors = validator.validate_code_snippet(
                code, self.max_code_length
            )
            if not is_valid_code:
                return {
                    "success": False,
                    "error": f"Invalid code: {', '.join(code_errors)}",
                    "error_type": "validation_error",
                }

            logger.info(
                f"Checking compliance for {language}/{framework} "
                f"code ({len(code)} chars)"
            )

            # 加载规范文档
            document = await self._load_standard_document(language, framework)
            if not document:
                return {
                    "success": False,
                    "error": f"Standard not found for {language}/{framework}",
                    "error_type": "not_found",
                }

            # 获取要检查的规则
            rules_to_check = self._get_rules_to_check(document, rule_ids)
            if not rules_to_check:
                return {
                    "success": False,
                    "error": "No rules found to check",
                    "error_type": "configuration_error",
                }

            # 执行合规性检查
            violations = await self._perform_compliance_check(
                code, rules_to_check, language
            )

            # 计算合规性统计
            stats = self._calculate_compliance_stats(violations, rules_to_check)

            return {
                "success": True,
                "compliance_result": {
                    "violations": [v.to_dict() for v in violations],
                    "violation_count": len(violations),
                    "rules_checked": len(rules_to_check),
                    "compliance_score": stats["compliance_score"],
                    "severity_breakdown": stats["severity_breakdown"],
                },
                "code_info": {
                    "language": language,
                    "framework": framework,
                    "line_count": len(code.split("\n")),
                    "character_count": len(code),
                },
                "standard_info": {
                    "version": document.metadata.version,
                    "source": document.metadata.source,
                    "last_updated": document.metadata.last_updated.isoformat(),
                },
            }

        except Exception as e:
            logger.error(f"Error checking compliance: {e}")
            return {"success": False, "error": str(e), "error_type": "unexpected_error"}

    async def explain_violation(
        self, rule_id: str, language: str, framework: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        解释违规规则

        Args:
            rule_id: 规则标识符
            language: 编程语言
            framework: 可选的框架名称

        Returns:
            包含规则解释的字典
        """
        try:
            # 验证输入参数
            if not validator.validate_rule_id(rule_id):
                return {
                    "success": False,
                    "error": f"Invalid rule ID format: {rule_id}",
                    "error_type": "validation_error",
                }

            if not validator.validate_language(language):
                return {
                    "success": False,
                    "error": f"Unsupported language: {language}",
                    "error_type": "validation_error",
                }

            logger.info(
                f"Explaining violation for rule: {rule_id} in {language}/{framework}"
            )

            # 加载规范文档
            document = await self._load_standard_document(language, framework)
            if not document:
                return {
                    "success": False,
                    "error": f"Standard not found for {language}/{framework}",
                    "error_type": "not_found",
                }

            # 查找规则
            rule = document.find_rule_by_id(rule_id)
            if not rule:
                return {
                    "success": False,
                    "error": f"Rule not found: {rule_id}",
                    "error_type": "not_found",
                }

            # 构建详细解释
            explanation = {
                "rule_info": {
                    "id": rule.id,
                    "title": rule.title,
                    "description": rule.description,
                    "severity": rule.severity.value,
                    "tags": rule.tags,
                },
                "why_important": self._explain_rule_importance(rule),
                "common_violations": self._get_common_violations(rule),
                "how_to_fix": self._get_fix_suggestions(rule),
                "examples": [example.to_dict() for example in rule.examples],
                "related_rules": await self._get_related_rules_info(document, rule),
            }

            return {
                "success": True,
                "explanation": explanation,
                "standard_info": {
                    "language": language,
                    "framework": framework,
                    "version": document.metadata.version,
                    "source": document.metadata.source,
                },
            }

        except Exception as e:
            logger.error(f"Error explaining violation: {e}")
            return {"success": False, "error": str(e), "error_type": "unexpected_error"}

    async def _load_standard_document(
        self, language: str, framework: Optional[str] = None
    ) -> Optional[StandardDocument]:
        """加载规范文档"""
        try:
            file_path = config.get_standard_file_path(language, framework)
            document_data = await file_manager.read_json(file_path)

            if document_data:
                return StandardDocument.from_dict(document_data)

            return None

        except Exception as e:
            logger.error(f"Error loading standard document: {e}")
            return None

    def _get_rules_to_check(
        self, document: StandardDocument, rule_ids: Optional[List[str]] = None
    ) -> List[StandardRule]:
        """获取要检查的规则"""
        all_rules = document.get_all_rules()

        if rule_ids:
            # 只检查指定的规则
            return [rule for rule in all_rules if rule.id in rule_ids]
        else:
            # 检查所有规则
            return all_rules

    async def _perform_compliance_check(
        self, code: str, rules: List[StandardRule], language: str
    ) -> List[ComplianceViolation]:
        """执行合规性检查"""
        violations = []
        code_lines = code.split("\n")

        for rule in rules:
            rule_violations = await self._check_rule_compliance(
                code, code_lines, rule, language
            )
            violations.extend(rule_violations)

        return violations

    async def _check_rule_compliance(
        self, code: str, code_lines: List[str], rule: StandardRule, language: str
    ) -> List[ComplianceViolation]:
        """检查单个规则的合规性"""
        violations = []

        # 基于规则描述和示例进行简单的模式匹配检查
        # 这里实现一些基本的检查逻辑

        # 检查命名规范
        if "naming" in rule.title.lower() or "name" in rule.tags:
            violations.extend(
                self._check_naming_conventions(code_lines, rule, language)
            )

        # 检查缩进规范
        if "indent" in rule.title.lower() or "indentation" in rule.tags:
            violations.extend(self._check_indentation(code_lines, rule))

        # 检查行长度
        if "line length" in rule.title.lower() or "length" in rule.tags:
            violations.extend(self._check_line_length(code_lines, rule))

        # 检查注释规范
        if "comment" in rule.title.lower() or "documentation" in rule.tags:
            violations.extend(self._check_comments(code_lines, rule, language))

        return violations

    def _check_naming_conventions(
        self, code_lines: List[str], rule: StandardRule, language: str
    ) -> List[ComplianceViolation]:
        """检查命名规范"""
        violations = []

        # 简化的命名检查逻辑
        for i, line in enumerate(code_lines):
            line = line.strip()

            # 检查变量命名（简化版）
            if language == "python":
                # Python变量应该使用snake_case
                var_pattern = r"(\w+)\s*="
                matches = re.finditer(var_pattern, line)
                for match in matches:
                    var_name = match.group(1)
                    if not re.match(
                        r"^[a-z_][a-z0-9_]*$", var_name
                    ) and var_name not in ["True", "False", "None"]:
                        violations.append(
                            ComplianceViolation(
                                rule_id=rule.id,
                                rule_title=rule.title,
                                severity=rule.severity,
                                line_number=i + 1,
                                column=match.start() + 1,
                                message=f"Variable '{var_name}' should use "
                                f"snake_case naming",
                                code_snippet=line,
                                suggestion=f"Consider renaming to: "
                                f"{self._to_snake_case(var_name)}",
                            )
                        )

        return violations

    def _check_indentation(
        self, code_lines: List[str], rule: StandardRule
    ) -> List[ComplianceViolation]:
        """检查缩进规范"""
        violations = []

        for i, line in enumerate(code_lines):
            if line.strip():  # 忽略空行
                # 检查是否使用制表符
                if "\t" in line:
                    violations.append(
                        ComplianceViolation(
                            rule_id=rule.id,
                            rule_title=rule.title,
                            severity=rule.severity,
                            line_number=i + 1,
                            column=line.index("\t") + 1,
                            message="Use spaces instead of tabs for indentation",
                            code_snippet=line,
                            suggestion="Replace tabs with 4 spaces",
                        )
                    )

        return violations

    def _check_line_length(
        self, code_lines: List[str], rule: StandardRule
    ) -> List[ComplianceViolation]:
        """检查行长度"""
        violations = []
        max_length = 80  # 默认最大行长度

        for i, line in enumerate(code_lines):
            if len(line) > max_length:
                violations.append(
                    ComplianceViolation(
                        rule_id=rule.id,
                        rule_title=rule.title,
                        severity=rule.severity,
                        line_number=i + 1,
                        column=max_length + 1,
                        message=f"Line too long ({len(line)} > "
                        f"{max_length} characters)",
                        code_snippet=line,
                        suggestion="Break long line into multiple lines",
                    )
                )

        return violations

    def _check_comments(
        self, code_lines: List[str], rule: StandardRule, language: str
    ) -> List[ComplianceViolation]:
        """检查注释规范"""
        violations = []

        # 简化的注释检查
        for i, line in enumerate(code_lines):
            line = line.strip()

            # 检查函数是否有文档字符串（Python）
            if language == "python" and line.startswith("def "):
                # 检查下一行是否是文档字符串
                if i + 1 < len(code_lines):
                    next_line = code_lines[i + 1].strip()
                    if not (next_line.startswith('"""') or next_line.startswith("'''")):
                        violations.append(
                            ComplianceViolation(
                                rule_id=rule.id,
                                rule_title=rule.title,
                                severity=rule.severity,
                                line_number=i + 1,
                                column=1,
                                message="Function should have a docstring",
                                code_snippet=line,
                                suggestion="Add a docstring describing the function",
                            )
                        )

        return violations

    def _calculate_compliance_stats(
        self, violations: List[ComplianceViolation], rules_checked: List[StandardRule]
    ) -> Dict[str, Any]:
        """计算合规性统计"""
        total_rules = len(rules_checked)
        violated_rules = len(set(v.rule_id for v in violations))

        compliance_score = (
            ((total_rules - violated_rules) / total_rules * 100)
            if total_rules > 0
            else 100
        )

        severity_breakdown = {"critical": 0, "high": 0, "medium": 0, "low": 0}

        for violation in violations:
            severity_breakdown[violation.severity.value] += 1

        return {
            "compliance_score": round(compliance_score, 2),
            "severity_breakdown": severity_breakdown,
        }

    def _explain_rule_importance(self, rule: StandardRule) -> str:
        """解释规则的重要性"""
        importance_map = {
            SeverityLevel.CRITICAL: "This rule is critical for code security "
            "and functionality.",
            SeverityLevel.HIGH: "This rule is important for code maintainability "
            "and readability.",
            SeverityLevel.MEDIUM: "This rule helps improve code quality "
            "and consistency.",
            SeverityLevel.LOW: "This rule is a best practice recommendation.",
        }

        return importance_map.get(
            rule.severity, "This rule helps maintain code quality."
        )

    def _get_common_violations(self, rule: StandardRule) -> List[str]:
        """获取常见违规情况"""
        # 基于规则描述生成常见违规情况
        common_violations = []

        if "naming" in rule.title.lower():
            common_violations.extend(
                [
                    "Using camelCase instead of snake_case",
                    "Using abbreviations in variable names",
                    "Not using descriptive names",
                ]
            )

        if "indent" in rule.title.lower():
            common_violations.extend(
                [
                    "Using tabs instead of spaces",
                    "Inconsistent indentation levels",
                    "Wrong number of spaces for indentation",
                ]
            )

        return common_violations[:3]  # 返回最多3个

    def _get_fix_suggestions(self, rule: StandardRule) -> List[str]:
        """获取修复建议"""
        suggestions = []

        # 从规则示例中提取修复建议
        for example in rule.examples:
            if example.good_code and example.explanation:
                suggestions.append(example.explanation)

        # 如果没有示例，提供通用建议
        if not suggestions:
            suggestions.append("Follow the rule description and best practices")

        return suggestions[:3]  # 返回最多3个建议

    async def _get_related_rules_info(
        self, document: StandardDocument, target_rule: StandardRule
    ) -> List[Dict[str, Any]]:
        """获取相关规则信息"""
        related_rules = []

        # 找到同一章节的其他规则
        for section in document.sections:
            if target_rule in section.get_all_rules():
                section_rules = section.get_all_rules()
                for rule in section_rules:
                    if rule.id != target_rule.id:
                        related_rules.append(
                            {
                                "id": rule.id,
                                "title": rule.title,
                                "severity": rule.severity.value,
                            }
                        )
                break

        return related_rules[:3]  # 返回最多3个相关规则

    def _to_snake_case(self, name: str) -> str:
        """转换为snake_case"""
        # 简单的camelCase到snake_case转换
        s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
        return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


# 全局合规性检查器实例
compliance_checker = ComplianceChecker()
