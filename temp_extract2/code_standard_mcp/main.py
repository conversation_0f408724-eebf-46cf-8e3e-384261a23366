#!/usr/bin/env python3
"""
CodeStandardMCP - 编码规范即服务

一个基于FastMCP 2.0的编码规范管理服务器，提供编码规范获取、管理等功能。
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional

# FastMCP 2.0 导入
from fastmcp import FastMCP

# 导入核心模块
from .config.settings import config
from .models.standard import StandardSelector
from .services.standard_fetcher import standard_fetcher
from .services.standards_manager import standards_manager

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        *(
            [logging.FileHandler(config.log_file)]
            if config.log_file
            else []
        ),
    ],
)

logger = logging.getLogger("CodeStandardMCP")


def create_error_response(
    error_type: str, message: str, **context
) -> Dict[str, Any]:
    """创建标准化的错误响应"""
    return {
        "success": False,
        "error": {
            "type": error_type,
            "message": message if not config.mask_error_details else "操作失败",
            "context": context if not config.mask_error_details else {},
        },
        "timestamp": "2024-01-01T00:00:00Z",  # 简化时间戳
    }


# 创建FastMCP应用实例
mcp = FastMCP(
    name=config.name,
    instructions="""
    CodeStandardMCP - 编码规范即服务

    这是一个简化的编码规范管理MCP服务器，提供以下核心功能：

    🔍 规范获取 (StandardFetcher)：
    - fetch_standard: 从外部API获取编码规范并缓存到本地

    🛠️ 规范管理 (StandardsManager)：
    - update_standards: 更新本地规范数据
    - clean_cache: 清理缓存和临时文件

    支持的编程语言：Python, JavaScript, Java, C#, Go, Rust, PHP, Ruby 等
    支持的框架：Django, React, Spring, .NET Core, Express 等

    环境变量配置：
    - CSMCP_DATA_DIR: 数据存储目录
    - CSMCP_CACHE_TTL: 缓存生存时间
    - CSMCP_LOG_LEVEL: 日志级别
    - CSMCP_HTTP_TIMEOUT: HTTP请求超时时间
    """,
    dependencies=[
        "httpx>=0.25.0",
        "pydantic>=2.0.0",
    ],
)

# ==================== MCP 工具实现 ====================


@mcp.tool(tags={"public", "standards", "fetcher"})
async def fetch_standard(
    language: str,
    framework: Optional[str] = None,
    version: Optional[str] = None,
    environment: Optional[str] = None,
    domain: Optional[str] = None,
    force_refresh: bool = False,
) -> str:
    """
    从外部API获取编码规范并缓存到本地

    这个工具从指定的外部API端点获取编码规范文档，支持多种编程语言和框架。
    获取的规范会被缓存到本地文件系统中以提高性能。

    Args:
        language: 编程语言（如 "python", "javascript", "java" 等）
        framework: 可选的框架名称（如 "django", "react", "spring" 等）
        version: 可选的版本信息（如 "3.8", "ES2020" 等）
        environment: 可选的环境类型（如 "服务端", "客户端" 等）
        domain: 可选的应用领域（如 "web开发", "数据科学" 等）
        force_refresh: 是否强制刷新，忽略现有缓存（默认：false）

    Returns:
        JSON格式的字符串，包含获取结果、规范信息和统计数据

    Examples:
        fetch_standard("python", "django")
        fetch_standard("javascript", "react", force_refresh=true)
    """
    try:
        logger.info(f"Fetching standard: {language}/{framework}")

        # 创建规范选择器
        selector = StandardSelector(
            language=language,
            framework=framework,
            version=version,
            environment=environment,
            domain=domain,
        )

        result = await standard_fetcher.fetch_standard(
            selector=selector,
            force_refresh=force_refresh,
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to fetch standard: {str(e)}",
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "management", "update"})
async def update_standards(
    language: Optional[str] = None,
    framework: Optional[str] = None,
    version: Optional[str] = None,
    environment: Optional[str] = None,
    domain: Optional[str] = None,
    force_all: bool = False,
) -> str:
    """
    更新本地规范数据

    这个工具更新本地存储的编码规范，可以选择性地更新特定语言/框架的规范，
    或者强制更新所有规范。

    Args:
        language: 可选的编程语言过滤器
        framework: 可选的框架过滤器
        version: 可选的版本过滤器
        environment: 可选的环境过滤器
        domain: 可选的领域过滤器
        force_all: 是否强制更新所有规范（默认：false）

    Returns:
        JSON格式的字符串，包含更新结果和统计信息

    Examples:
        update_standards()
        update_standards("python", "django")
        update_standards(force_all=true)
    """
    try:
        logger.info(f"Updating standards: {language}/{framework}")

        # 创建可选的规范选择器
        selector = None
        if language:
            selector = StandardSelector(
                language=language,
                framework=framework,
                version=version,
                environment=environment,
                domain=domain,
            )

        result = await standards_manager.update_standards(
            selector=selector,
            force_all=force_all,
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to update standards: {str(e)}",
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "management", "cache"})
async def clean_cache(older_than_days: int = 30) -> str:
    """
    清理缓存和临时文件

    这个工具清理过期的缓存文件和临时数据，释放磁盘空间。
    可以指定清理多少天前的缓存文件。

    Args:
        older_than_days: 清理多少天前的缓存（默认：30天）

    Returns:
        JSON格式的字符串，包含清理结果和统计信息

    Examples:
        clean_cache()
        clean_cache(7)  # 清理7天前的缓存
    """
    try:
        logger.info(f"Cleaning cache older than {older_than_days} days")

        result = await standards_manager.clean_cache(
            older_than_days=older_than_days
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to clean cache: {str(e)}",
            older_than_days=older_than_days,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


# ==================== 服务器启动 ====================

async def async_main():
    """异步主函数"""
    try:
        logger.info(f"Starting {config.name} v{config.version}")
        logger.info(f"Data directory: {config.data_dir}")
        logger.info(f"Cache directory: {config.cache_dir}")

        # 运行MCP服务器
        await mcp.run()

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


def main():
    """同步入口点函数"""
    try:
        # 检查是否已经在 asyncio 事件循环中
        loop = asyncio.get_running_loop()
        # 如果已经在事件循环中，直接创建任务
        import asyncio
        loop.create_task(async_main())
    except RuntimeError:
        # 如果没有运行的事件循环，使用 asyncio.run
        asyncio.run(async_main())


if __name__ == "__main__":
    main()
