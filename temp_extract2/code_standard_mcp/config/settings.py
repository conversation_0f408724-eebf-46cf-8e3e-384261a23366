"""
CodeStandardMCP 配置管理模块

提供简化的服务器配置功能
"""

import os
from pathlib import Path
from typing import Optional


class ServerConfig:
    """简化的服务器配置管理类"""

    def __init__(self):
        # 基础配置
        self.name = "CodeStandardMCP"
        self.version = "1.0.0"
        self.description = "编码规范服务工具"

        # 网络配置
        self.http_timeout = float(os.getenv("CSMCP_HTTP_TIMEOUT", "30.0"))
        self.user_agent = os.getenv("CSMCP_USER_AGENT", "CodeStandardMCP/1.0 (httpx)")
        self.max_retries = int(os.getenv("CSMCP_MAX_RETRIES", "3"))

        # 缓存配置
        self.cache_ttl_hours = int(os.getenv("CSMCP_CACHE_TTL", "24"))

        # 日志配置
        self.log_level = os.getenv("CSMCP_LOG_LEVEL", "INFO")
        self.log_file = os.getenv("CSMCP_LOG_FILE", None)
        self.mask_error_details = (
            os.getenv("CSMCP_MASK_ERROR_DETAILS", "false").lower() == "true"
        )

        # 存储配置
        self.data_dir = Path(os.getenv("CSMCP_DATA_DIR", "./data"))
        self.cache_dir = self.data_dir / "cache"

        # 确保目录存在
        self._ensure_directories()

        # API配置 - 可通过环境变量覆盖
        self.default_api_base = os.getenv("CSMCP_API_BASE", "https://api.example.com/standards")
        self.default_api_endpoints = {
            "python": f"{self.default_api_base}/python",
            "javascript": f"{self.default_api_base}/javascript",
            "java": f"{self.default_api_base}/java",
            "csharp": f"{self.default_api_base}/csharp",
            "go": f"{self.default_api_base}/go",
        }


    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.data_dir, self.cache_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"

    def get_api_endpoint(
        self, language: str, framework: Optional[str] = None
    ) -> Optional[str]:
        """获取指定语言和框架的API端点"""
        key = f"{language}_{framework}" if framework else language
        return self.default_api_endpoints.get(key.lower())


# 全局配置实例
config = ServerConfig()
