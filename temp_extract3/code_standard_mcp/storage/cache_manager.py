"""
缓存管理模块

实现编码规范的简化本地缓存功能
"""

import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from ..config.settings import config
from .file_manager import file_manager

logger = logging.getLogger("CodeStandardMCP.CacheManager")


class CacheManager:
    """简化的缓存管理器"""

    def __init__(self):
        self.cache_dir = config.cache_dir

    def generate_cache_key(self, url: str, domain: str = "", additional_params: Dict[str, Any] = None) -> str:
        """生成缓存键"""
        key_parts = [url, domain]
        if additional_params:
            # 将参数按键排序以确保一致性
            sorted_params = sorted(additional_params.items())
            key_parts.extend([f"{k}={v}" for k, v in sorted_params])
        
        key_string = "|".join(str(part) for part in key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    async def get_cached_content(
        self, 
        url: str, 
        domain: str = "", 
        additional_params: Dict[str, Any] = None
    ) -> Optional[Any]:
        """获取缓存内容"""
        try:
            cache_key = self.generate_cache_key(url, domain, additional_params)
            cache_file_path = config.get_cache_file_path(cache_key)
            
            if not cache_file_path.exists():
                return None
                
            # 读取缓存文件
            data = await file_manager.read_json(cache_file_path)
            if not data:
                return None
                
            # 检查缓存是否过期
            if self._is_cache_expired(data):
                # 删除过期缓存
                cache_file_path.unlink()
                return None
                
            return data.get("content")
            
        except Exception as e:
            logger.error(f"Error reading cached content: {e}")
            return None

    async def store_cached_content(
        self,
        url: str,
        content: Any,
        domain: str = "",
        content_type: str = "json",
        additional_params: Dict[str, Any] = None,
    ) -> bool:
        """存储内容到缓存"""
        try:
            cache_key = self.generate_cache_key(url, domain, additional_params)
            cache_file_path = config.get_cache_file_path(cache_key)
            
            # 确保缓存目录存在
            cache_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 准备缓存数据
            cache_data = {
                "content": content,
                "metadata": {
                    "url": url,
                    "domain": domain,
                    "content_type": content_type,
                    "additional_params": additional_params or {},
                    "cached_at": datetime.now().isoformat(),
                    "cache_key": cache_key,
                }
            }
            
            # 写入缓存文件
            success = await file_manager.write_json(cache_file_path, cache_data)
            
            if success:
                logger.debug(f"Content cached successfully with key: {cache_key}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error storing cached content: {e}")
            return False

    async def clear_cache(self, cache_key: Optional[str] = None) -> bool:
        """清理缓存"""
        try:
            if cache_key:
                # 清理特定缓存
                cache_file_path = config.get_cache_file_path(cache_key)
                if cache_file_path.exists():
                    cache_file_path.unlink()
                    logger.info(f"Cleared cache: {cache_key}")
                return True
            else:
                # 清理所有缓存
                if self.cache_dir.exists():
                    for cache_file in self.cache_dir.glob("*.json"):
                        cache_file.unlink()
                    logger.info("Cleared all cache files")
                return True
                
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False

    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if not self.cache_dir.exists():
                return {
                    "total_files": 0,
                    "total_size_bytes": 0,
                    "total_size_mb": 0,
                }
            
            total_files = 0
            total_size = 0
            
            for cache_file in self.cache_dir.glob("*.json"):
                if cache_file.is_file():
                    total_files += 1
                    total_size += cache_file.stat().st_size
            
            return {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {
                "total_files": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "error": str(e),
            }

    def _is_cache_expired(self, cache_data: Dict[str, Any]) -> bool:
        """检查缓存是否过期"""
        try:
            cached_at_str = cache_data.get("metadata", {}).get("cached_at")
            if not cached_at_str:
                return True
                
            cached_at = datetime.fromisoformat(cached_at_str)
            cache_ttl = config.cache_ttl_hours
            
            # 检查是否超过TTL
            age_hours = (datetime.now() - cached_at).total_seconds() / 3600
            return age_hours > cache_ttl
            
        except Exception as e:
            logger.error(f"Error checking cache expiration: {e}")
            return True  # 出错时认为已过期


# 创建全局实例
cache_manager = CacheManager()
