#!/usr/bin/env python3
"""
测试STDIO MCP服务器的简单脚本
"""

import asyncio
import json
import subprocess
import sys
from pathlib import Path


async def test_stdio_server():
    """测试STDIO MCP服务器"""
    print("🧪 Testing STDIO MCP server...")
    
    # 启动服务器进程
    process = await asyncio.create_subprocess_exec(
        sys.executable, "main.py",
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
        cwd=Path(__file__).parent
    )
    
    try:
        # 发送初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # 发送请求
        request_data = json.dumps(init_request) + "\n"
        process.stdin.write(request_data.encode())
        await process.stdin.drain()
        
        # 读取响应 (设置超时)
        try:
            response_data = await asyncio.wait_for(
                process.stdout.readline(), 
                timeout=10.0
            )
            
            if response_data:
                response = json.loads(response_data.decode().strip())
                print(f"✅ Server responded: {response}")
                
                # 检查是否是有效的初始化响应
                if "result" in response and "capabilities" in response["result"]:
                    print("✅ STDIO MCP server is working correctly!")
                    return True
                else:
                    print(f"❌ Unexpected response format: {response}")
                    return False
            else:
                print("❌ No response from server")
                return False
                
        except asyncio.TimeoutError:
            print("❌ Server response timeout")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # 清理进程
        try:
            process.terminate()
            await asyncio.wait_for(process.wait(), timeout=5.0)
        except asyncio.TimeoutError:
            process.kill()
            await process.wait()


async def main():
    """主函数"""
    success = await test_stdio_server()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
