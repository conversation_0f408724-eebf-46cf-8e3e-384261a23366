{"metadata": {"version": "1.0.0", "created_at": "2025-01-01T00:00:00Z", "last_updated": "2025-01-01T00:00:00Z", "total_standards": 1, "total_rules": 8, "supported_languages": ["python"]}, "languages": {"python": {"standards": [{"file_path": "data/standards/python_example.json", "framework": null, "version": "1.0.0", "source": "PEP 8 - Style Guide for Python Code", "last_updated": "2025-01-01T00:00:00Z", "rule_count": 8, "sections": ["naming", "formatting", "documentation"], "tags": ["pep8", "style", "formatting", "naming"], "checksum": "example_checksum_python"}], "frameworks": {}, "total_rules": 8, "last_updated": "2025-01-01T00:00:00Z"}}, "search_index": {"keywords": {"naming": {"python": [{"rule_id": "PEP8-N001", "section": "naming", "title": "变量命名使用snake_case", "relevance": 1.0}, {"rule_id": "PEP8-N002", "section": "naming", "title": "常量命名使用UPPER_CASE", "relevance": 1.0}, {"rule_id": "PEP8-N003", "section": "naming", "title": "类名使用PascalCase", "relevance": 1.0}]}, "formatting": {"python": [{"rule_id": "PEP8-F001", "section": "formatting", "title": "行长度限制", "relevance": 1.0}, {"rule_id": "PEP8-F002", "section": "formatting", "title": "缩进使用4个空格", "relevance": 1.0}, {"rule_id": "PEP8-F003", "section": "formatting", "title": "空行使用规范", "relevance": 1.0}]}, "documentation": {"python": [{"rule_id": "PEP8-D001", "section": "documentation", "title": "函数文档字符串", "relevance": 1.0}, {"rule_id": "PEP8-D002", "section": "documentation", "title": "类文档字符串", "relevance": 1.0}]}, "variables": {"python": [{"rule_id": "PEP8-N001", "section": "naming", "title": "变量命名使用snake_case", "relevance": 0.9}]}, "constants": {"python": [{"rule_id": "PEP8-N002", "section": "naming", "title": "常量命名使用UPPER_CASE", "relevance": 0.9}]}, "classes": {"python": [{"rule_id": "PEP8-N003", "section": "naming", "title": "类名使用PascalCase", "relevance": 0.9}, {"rule_id": "PEP8-D002", "section": "documentation", "title": "类文档字符串", "relevance": 0.8}]}, "functions": {"python": [{"rule_id": "PEP8-D001", "section": "documentation", "title": "函数文档字符串", "relevance": 0.9}]}, "indentation": {"python": [{"rule_id": "PEP8-F002", "section": "formatting", "title": "缩进使用4个空格", "relevance": 0.9}]}, "line-length": {"python": [{"rule_id": "PEP8-F001", "section": "formatting", "title": "行长度限制", "relevance": 0.9}]}, "docstrings": {"python": [{"rule_id": "PEP8-D001", "section": "documentation", "title": "函数文档字符串", "relevance": 0.9}, {"rule_id": "PEP8-D002", "section": "documentation", "title": "类文档字符串", "relevance": 0.9}]}}, "tags": {"pep8": {"python": [{"rule_id": "PEP8-N001", "section": "naming", "title": "变量命名使用snake_case", "relevance": 1.0}, {"rule_id": "PEP8-N002", "section": "naming", "title": "常量命名使用UPPER_CASE", "relevance": 1.0}, {"rule_id": "PEP8-N003", "section": "naming", "title": "类名使用PascalCase", "relevance": 1.0}, {"rule_id": "PEP8-F001", "section": "formatting", "title": "行长度限制", "relevance": 1.0}, {"rule_id": "PEP8-F002", "section": "formatting", "title": "缩进使用4个空格", "relevance": 1.0}, {"rule_id": "PEP8-F003", "section": "formatting", "title": "空行使用规范", "relevance": 1.0}, {"rule_id": "PEP8-D001", "section": "documentation", "title": "函数文档字符串", "relevance": 1.0}, {"rule_id": "PEP8-D002", "section": "documentation", "title": "类文档字符串", "relevance": 1.0}]}}}, "statistics": {"rules_by_severity": {"critical": 0, "high": 2, "medium": 5, "low": 1}, "rules_by_language": {"python": 8}, "sections_by_language": {"python": 3}}}