{"metadata": {"language": "python", "framework": null, "version": "1.0.0", "source": "PEP 8 - Style Guide for Python Code", "url": "https://peps.python.org/pep-0008/", "last_updated": "2025-01-01T00:00:00Z", "description": "Python编码规范示例文档", "tags": ["pep8", "style", "formatting", "naming"]}, "sections": [{"id": "naming", "title": "命名规范", "description": "Python中的命名约定", "rules": [{"id": "PEP8-N001", "title": "变量命名使用snake_case", "description": "变量名应该使用小写字母，单词之间用下划线分隔", "severity": "medium", "tags": ["naming", "variables"], "examples": [{"good_code": "user_name = '<PERSON>'\ntotal_count = 42", "bad_code": "userName = 'John'\ntotalCount = 42", "explanation": "使用snake_case而不是camelCase"}]}, {"id": "PEP8-N002", "title": "常量命名使用UPPER_CASE", "description": "常量应该使用全大写字母，单词之间用下划线分隔", "severity": "medium", "tags": ["naming", "constants"], "examples": [{"good_code": "MAX_SIZE = 100\nDEFAULT_TIMEOUT = 30", "bad_code": "maxSize = 100\ndefaultTimeout = 30", "explanation": "常量使用全大写字母"}]}, {"id": "PEP8-N003", "title": "类名使用PascalCase", "description": "类名应该使用首字母大写的驼峰命名法", "severity": "high", "tags": ["naming", "classes"], "examples": [{"good_code": "class UserManager:\n    pass\n\nclass DatabaseConnection:\n    pass", "bad_code": "class user_manager:\n    pass\n\nclass database_connection:\n    pass", "explanation": "类名使用PascalCase"}]}]}, {"id": "formatting", "title": "代码格式", "description": "代码格式和布局规范", "rules": [{"id": "PEP8-F001", "title": "行长度限制", "description": "每行代码不应超过79个字符", "severity": "medium", "tags": ["formatting", "line-length"], "examples": [{"good_code": "# 适当的行长度\nresult = some_function(param1, param2,\n                      param3, param4)", "bad_code": "# 行太长\nresult = some_function(param1, param2, param3, param4, param5, param6, param7)", "explanation": "将长行分解为多行"}]}, {"id": "PEP8-F002", "title": "缩进使用4个空格", "description": "使用4个空格进行缩进，不要使用制表符", "severity": "high", "tags": ["formatting", "indentation"], "examples": [{"good_code": "def my_function():\n    if True:\n        return 'correct'", "bad_code": "def my_function():\n\tif True:\n\t\treturn 'incorrect'", "explanation": "使用4个空格而不是制表符"}]}, {"id": "PEP8-F003", "title": "空行使用规范", "description": "顶级函数和类定义前后用两个空行分隔", "severity": "low", "tags": ["formatting", "blank-lines"], "examples": [{"good_code": "class MyClass:\n    pass\n\n\ndef my_function():\n    pass\n\n\nclass AnotherClass:\n    pass", "bad_code": "class MyClass:\n    pass\ndef my_function():\n    pass\nclass AnotherClass:\n    pass", "explanation": "在类和函数定义之间使用两个空行"}]}]}, {"id": "documentation", "title": "文档规范", "description": "代码文档和注释规范", "rules": [{"id": "PEP8-D001", "title": "函数文档字符串", "description": "所有公共函数都应该有文档字符串", "severity": "medium", "tags": ["documentation", "docstrings"], "examples": [{"good_code": "def calculate_area(radius):\n    \"\"\"计算圆的面积。\n    \n    Args:\n        radius (float): 圆的半径\n    \n    Returns:\n        float: 圆的面积\n    \"\"\"\n    return 3.14159 * radius ** 2", "bad_code": "def calculate_area(radius):\n    return 3.14159 * radius ** 2", "explanation": "为函数添加描述性的文档字符串"}]}, {"id": "PEP8-D002", "title": "类文档字符串", "description": "所有公共类都应该有文档字符串", "severity": "medium", "tags": ["documentation", "classes"], "examples": [{"good_code": "class UserManager:\n    \"\"\"用户管理类。\n    \n    负责用户的创建、更新和删除操作。\n    \"\"\"\n    \n    def __init__(self):\n        pass", "bad_code": "class UserManager:\n    \n    def __init__(self):\n        pass", "explanation": "为类添加描述性的文档字符串"}]}]}]}