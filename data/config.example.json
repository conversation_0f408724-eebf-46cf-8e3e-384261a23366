{"server": {"name": "CodeStandardMCP", "version": "1.0.0", "description": "编码规范即服务 MCP 服务器", "host": "0.0.0.0", "port": 18023, "log_level": "INFO"}, "data_paths": {"data_dir": "./data", "standards_dir": "./data/standards", "cache_dir": "./data/cache", "index_dir": "./data/index", "index_file": "./data/index/standards_index.json"}, "cache_settings": {"cache_ttl_hours": 24, "max_cache_size_mb": 100, "cleanup_interval_hours": 6, "auto_cleanup_enabled": true}, "http_settings": {"timeout_seconds": 30, "max_retries": 3, "retry_delay_seconds": 1, "user_agent": "CodeStandardMCP/1.0.0"}, "compliance_settings": {"max_code_length": 10000, "context_lines": 3, "default_severity_threshold": "medium"}, "supported_languages": {"python": {"extensions": [".py"], "frameworks": ["django", "flask", "<PERSON><PERSON><PERSON>"], "api_endpoints": {"pep8": "https://api.example.com/standards/python/pep8", "django": "https://api.example.com/standards/python/django"}}, "javascript": {"extensions": [".js", ".jsx", ".ts", ".tsx"], "frameworks": ["react", "vue", "angular", "express"], "api_endpoints": {"eslint": "https://api.example.com/standards/javascript/eslint", "react": "https://api.example.com/standards/javascript/react"}}, "java": {"extensions": [".java"], "frameworks": ["spring", "hibernate", "maven"], "api_endpoints": {"google": "https://api.example.com/standards/java/google", "spring": "https://api.example.com/standards/java/spring"}}, "csharp": {"extensions": [".cs"], "frameworks": ["dotnet", "aspnet", "entity"], "api_endpoints": {"microsoft": "https://api.example.com/standards/csharp/microsoft", "dotnet": "https://api.example.com/standards/csharp/dotnet"}}, "go": {"extensions": [".go"], "frameworks": ["gin", "echo", "fiber"], "api_endpoints": {"effective": "https://api.example.com/standards/go/effective", "google": "https://api.example.com/standards/go/google"}}, "rust": {"extensions": [".rs"], "frameworks": ["actix", "rocket", "warp"], "api_endpoints": {"official": "https://api.example.com/standards/rust/official", "clippy": "https://api.example.com/standards/rust/clippy"}}, "php": {"extensions": [".php"], "frameworks": ["laravel", "symfony", "codeigniter"], "api_endpoints": {"psr": "https://api.example.com/standards/php/psr", "laravel": "https://api.example.com/standards/php/laravel"}}, "ruby": {"extensions": [".rb"], "frameworks": ["rails", "sinatra", "grape"], "api_endpoints": {"rubocop": "https://api.example.com/standards/ruby/rubocop", "rails": "https://api.example.com/standards/ruby/rails"}}}, "update_settings": {"auto_update_enabled": true, "auto_update_interval_days": 7, "check_update_on_startup": true, "backup_before_update": true}, "search_settings": {"max_results": 50, "context_lines_before": 2, "context_lines_after": 2, "fuzzy_search_enabled": true, "search_timeout_seconds": 10}, "validation_settings": {"strict_mode": false, "validate_on_fetch": true, "validate_on_update": true, "max_validation_errors": 100}}