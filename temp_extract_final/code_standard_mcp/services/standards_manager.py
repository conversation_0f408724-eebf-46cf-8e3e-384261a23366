"""
编码规范管理服务

实现规范更新、验证、缓存清理等管理功能
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..config.settings import config
from ..models.standard import StandardDocument, StandardSelector
from ..storage.cache_manager import cache_manager
from ..storage.file_manager import file_manager

logger = logging.getLogger("CodeStandardMCP.StandardsManager")


class StandardsManager:
    """编码规范管理服务类"""

    def __init__(self):
        self.cache_index_file = config.cache_dir / "cache_index.json"

    async def update_standards(
        self,
        selector: Optional[StandardSelector] = None,
        force_all: bool = False,
    ) -> Dict[str, Any]:
        """
        更新本地规范数据

        Args:
            selector: 可选的StandardSelector类型，用于筛选需要更新的规范
            force_all: 是否强制更新所有规范

        Returns:
            更新结果摘要
        """
        try:
            logger.info("Starting standards update")
            
            updated_count = 0
            failed_count = 0
            results = []

            if force_all or not selector:
                # 更新所有缓存的规范
                cache_entries = await self._get_all_cache_entries()
                for entry in cache_entries:
                    try:
                        entry_selector = StandardSelector.model_validate(entry["selector"])
                        if not selector or self._matches_selector(entry_selector, selector):
                            # 使用StandardFetcher更新
                            from .standard_fetcher import standard_fetcher
                            result = await standard_fetcher.fetch_standard(
                                entry_selector, force_refresh=True
                            )
                            if result["success"]:
                                updated_count += 1
                                results.append({
                                    "selector": entry_selector.model_dump(),
                                    "status": "updated",
                                })
                            else:
                                failed_count += 1
                                results.append({
                                    "selector": entry_selector.model_dump(),
                                    "status": "failed",
                                    "error": result.get("error", "Unknown error"),
                                })
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"Error updating entry: {e}")

            else:
                # 更新特定规范
                from .standard_fetcher import standard_fetcher
                result = await standard_fetcher.fetch_standard(selector, force_refresh=True)
                if result["success"]:
                    updated_count = 1
                    results.append({
                        "selector": selector.model_dump(),
                        "status": "updated",
                    })
                else:
                    failed_count = 1
                    results.append({
                        "selector": selector.model_dump(),
                        "status": "failed",
                        "error": result.get("error", "Unknown error"),
                    })

            return {
                "success": True,
                "summary": {
                    "updated_count": updated_count,
                    "failed_count": failed_count,
                    "total_processed": updated_count + failed_count,
                },
                "results": results,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error updating standards: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error",
            }

    async def clean_cache(self, older_than_days: int = 30) -> Dict[str, Any]:
        """
        清理缓存和临时文件

        Args:
            older_than_days: 清理多少天前的缓存

        Returns:
            清理结果摘要
        """
        try:
            logger.info(f"Starting cache cleanup (older than {older_than_days} days)")
            
            cutoff_date = datetime.now() - timedelta(days=older_than_days)
            cleaned_count = 0
            freed_bytes = 0
            
            # 获取所有缓存条目
            cache_entries = await self._get_all_cache_entries()
            remaining_entries = []
            
            for entry in cache_entries:
                try:
                    fetch_date = datetime.fromisoformat(entry["fetch_date"])
                    cache_key = entry["cache_key"]
                    cache_file_path = config.get_cache_file_path(cache_key)
                    
                    if fetch_date < cutoff_date and cache_file_path.exists():
                        # 删除过期缓存文件
                        file_size = cache_file_path.stat().st_size
                        cache_file_path.unlink()
                        cleaned_count += 1
                        freed_bytes += file_size
                        logger.debug(f"Cleaned cache file: {cache_key}")
                    else:
                        remaining_entries.append(entry)
                        
                except Exception as e:
                    logger.error(f"Error processing cache entry: {e}")
                    remaining_entries.append(entry)  # 保留有问题的条目
            
            # 更新缓存索引
            await self._save_cache_index(remaining_entries)
            
            return {
                "success": True,
                "summary": {
                    "cleaned_files": cleaned_count,
                    "freed_bytes": freed_bytes,
                    "freed_mb": round(freed_bytes / (1024 * 1024), 2),
                    "remaining_files": len(remaining_entries),
                },
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error cleaning cache: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error",
            }

    async def _get_all_cache_entries(self) -> List[Dict[str, Any]]:
        """获取所有缓存条目"""
        try:
            if not self.cache_index_file.exists():
                return []
                
            data = await file_manager.read_json(self.cache_index_file)
            if not data or "entries" not in data:
                return []
                
            return data["entries"]
        except Exception as e:
            logger.error(f"Error reading cache index: {e}")
            return []

    async def _save_cache_index(self, entries: List[Dict[str, Any]]) -> bool:
        """保存缓存索引"""
        try:
            index_data = {
                "entries": entries,
                "last_update_check": datetime.now().isoformat(),
            }
            
            # 确保目录存在
            self.cache_index_file.parent.mkdir(parents=True, exist_ok=True)
            
            return await file_manager.write_json(self.cache_index_file, index_data)
        except Exception as e:
            logger.error(f"Error saving cache index: {e}")
            return False

    def _matches_selector(self, entry_selector: StandardSelector, filter_selector: StandardSelector) -> bool:
        """检查选择器是否匹配"""
        if filter_selector.language and entry_selector.language != filter_selector.language:
            return False
        if filter_selector.framework and entry_selector.framework != filter_selector.framework:
            return False
        if filter_selector.version and entry_selector.version != filter_selector.version:
            return False
        if filter_selector.environment and entry_selector.environment != filter_selector.environment:
            return False
        if filter_selector.domain and entry_selector.domain != filter_selector.domain:
            return False
        return True


# 创建全局实例
standards_manager = StandardsManager()
