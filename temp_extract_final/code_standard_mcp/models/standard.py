"""
编码规范数据模型

定义编码规范相关的简化数据结构
"""

from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class StandardSelector(BaseModel):
    """编码规范选择器"""
    
    language: str = Field(..., description="编程语言")
    framework: Optional[str] = Field(None, description="框架名称(可选)")
    version: Optional[str] = Field(None, description="版本信息(可选)")
    environment: Optional[str] = Field(None, description="环境类型(可选，如'服务端'、'客户端')")
    domain: Optional[str] = Field(None, description="应用领域(可选，如'web开发'、'数据科学')")

    def to_cache_key(self) -> str:
        """生成缓存键"""
        parts = [self.language]
        if self.framework:
            parts.append(self.framework)
        if self.version:
            parts.append(self.version)
        if self.environment:
            parts.append(self.environment)
        if self.domain:
            parts.append(self.domain)
        return "_".join(parts).lower()


class StandardMetadata(BaseModel):
    """编码规范元数据"""
    
    selector: StandardSelector = Field(..., description="规范选择器")
    fetch_date: datetime = Field(default_factory=datetime.now, description="获取日期")
    source_url: Optional[str] = Field(None, description="源URL")
    content_hash: Optional[str] = Field(None, description="内容哈希")


class StandardDocument(BaseModel):
    """简化的编码规范文档"""
    
    metadata: StandardMetadata = Field(..., description="规范元数据")
    content: str = Field(..., description="规范内容文本")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "metadata": {
                "selector": {
                    "language": self.metadata.selector.language,
                    "framework": self.metadata.selector.framework,
                    "version": self.metadata.selector.version,
                    "environment": self.metadata.selector.environment,
                    "domain": self.metadata.selector.domain,
                },
                "fetch_date": self.metadata.fetch_date.isoformat(),
                "source_url": self.metadata.source_url,
                "content_hash": self.metadata.content_hash,
            },
            "content": self.content,
        }
