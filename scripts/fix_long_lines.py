#!/usr/bin/env python3
"""
修复长行问题的脚本
"""

import re
import subprocess
from pathlib import Path


def run_flake8():
    """运行flake8获取长行错误"""
    result = subprocess.run(
        ["uv", "run", "flake8", "main.py", "src/", "--select=E501"],
        capture_output=True,
        text=True,
        cwd=Path(__file__).parent.parent
    )
    return result.stdout


def parse_flake8_output(output):
    """解析flake8输出，提取长行信息"""
    long_lines = []
    for line in output.strip().split('\n'):
        if line and 'E501' in line:
            # 解析格式: file:line:col: E501 line too long (X > Y characters)
            match = re.match(r'([^:]+):(\d+):\d+: E501 line too long \((\d+) > \d+ characters\)', line)
            if match:
                file_path, line_num, length = match.groups()
                long_lines.append({
                    'file': file_path,
                    'line': int(line_num),
                    'length': int(length)
                })
    return long_lines


def main():
    """主函数"""
    print("🔍 检查长行问题...")
    
    output = run_flake8()
    if not output.strip():
        print("✅ 没有发现长行问题！")
        return
    
    long_lines = parse_flake8_output(output)
    print(f"📋 发现 {len(long_lines)} 个长行问题:")
    
    for item in long_lines:
        print(f"  {item['file']}:{item['line']} ({item['length']} 字符)")
    
    print("\n💡 建议手动修复这些长行，通常可以:")
    print("  1. 将长字符串分割为多行")
    print("  2. 将长函数调用参数分行")
    print("  3. 将长条件表达式分行")


if __name__ == "__main__":
    main()
