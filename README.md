# CodeStandardMCP

一个基于FastMCP 2.0的简化编码规范管理服务器，提供编码规范获取和管理功能。

## 🚀 快速开始

### 环境要求
- Python 3.8+
- [uv](https://docs.astral.sh/uv/) 包管理器

### 安装和运行

```bash
# 1. 克隆项目
git clone <repository-url>
cd CodeStandardMCP

# 2. 使用uv安装依赖
uv sync

# 3. 直接运行开发版本
uv run python main.py

# 4. 或者构建wheel包并使用uvx运行
uv build
uvx dist/codestandardmcp-1.0.0-py3-none-any.whl
```

## 📋 功能特性

### 🔍 核心工具

1. **fetch_standard** - 编码规范获取
   - 从外部API获取编码规范并缓存到本地
   - 支持多种编程语言：Python, JavaScript, Java, C#, Go
   - 支持框架特定规范：Django, React, Spring等
   - 智能缓存机制，避免重复请求

2. **update_standards** - 规范管理
   - 批量更新本地规范数据
   - 支持选择性更新特定语言或框架
   - 自动验证和索引更新

3. **clean_cache** - 缓存清理
   - 清理过期的缓存文件
   - 释放磁盘空间
   - 可配置清理策略

### 🎯 设计特点

- **极简架构**: 只保留核心功能，去除复杂性
- **高性能缓存**: 基于文件系统的简单缓存机制
- **环境变量配置**: 无需配置文件，通过环境变量灵活配置
- **标准化数据模型**: 使用Pydantic确保数据一致性

## 🛠️ 开发和构建

### 使用uv进行开发

```bash
# 安装依赖（包括开发依赖）
uv sync

# 运行代码格式化
uv run black main.py src/
uv run isort main.py src/

# 运行类型检查
uv run mypy main.py src/

# 运行测试
uv run pytest

# 启动开发服务器
uv run python main.py
```

### 构建和分发

```bash
# 构建wheel包
uv build

# 查看构建结果
ls dist/
# 输出: codestandardmcp-1.0.0-py3-none-any.whl

# 使用uvx运行wheel包
uvx dist/codestandardmcp-1.0.0-py3-none-any.whl

# 或者安装到全局环境
uv tool install dist/codestandardmcp-1.0.0-py3-none-any.whl
```

## 📊 使用示例

### MCP客户端调用

```python
# 获取Python编码规范
result = await mcp_client.call_tool("fetch_standard", {
    "language": "python",
    "framework": "django",
    "version": "4.2",
    "environment": "web",
    "domain": "backend"
})

# 更新所有规范
result = await mcp_client.call_tool("update_standards")

# 清理30天前的缓存
result = await mcp_client.call_tool("clean_cache", {
    "older_than_days": 30
})
```

### Claude Desktop配置

```json
{
  "mcpServers": {
    "codestandardmcp": {
      "command": "uvx",
      "args": ["path/to/codestandardmcp-1.0.0-py3-none-any.whl"],
      "env": {
        "CSMCP_DATA_DIR": "./data",
        "CSMCP_CACHE_TTL": "24",
        "CSMCP_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

## 🔧 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `CSMCP_DATA_DIR` | `./data` | 数据存储目录 |
| `CSMCP_CACHE_TTL` | `24` | 缓存生存时间（小时） |
| `CSMCP_LOG_LEVEL` | `INFO` | 日志级别 |
| `CSMCP_HTTP_TIMEOUT` | `30.0` | HTTP请求超时时间（秒） |
| `CSMCP_USER_AGENT` | `CodeStandardMCP/1.0` | HTTP用户代理 |
| `CSMCP_MAX_RETRIES` | `3` | 最大重试次数 |
| `CSMCP_API_BASE` | `https://api.example.com/standards` | API基础URL |

### 中国用户优化

项目已配置中国镜像源以提高下载速度：

```toml
# pyproject.toml中的uv配置
[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
```

## 🏗️ 项目结构

```
./
├── main.py                    # 主程序入口，定义3个MCP工具
├── pyproject.toml            # 项目配置，uv构建配置
├── uv.lock                   # 依赖锁定文件
├── README.md                 # 项目文档
├── Mcp.md                    # 设计文档
└── src/                      # 核心代码
    ├── config/               # 配置管理
    │   └── settings.py       # 服务器配置
    ├── models/               # 数据模型
    │   └── standard.py       # StandardSelector等模型
    ├── services/             # 核心服务
    │   ├── standard_fetcher.py    # 规范获取服务
    │   └── standards_manager.py   # 规范管理服务
    └── storage/              # 存储管理
        ├── cache_manager.py  # 缓存管理
        └── file_manager.py   # 文件管理
```

## 🎯 核心组件

### StandardSelector数据结构
```python
{
    "language": "python",      # 编程语言
    "framework": "django",     # 框架（可选）
    "version": "4.2",         # 版本（可选）
    "environment": "web",     # 环境（可选）
    "domain": "backend"       # 领域（可选）
}
```

### 缓存机制
- 基于StandardSelector生成唯一缓存键
- 文件系统存储，JSON格式
- 自动过期检查和清理
- 支持强制刷新

## 📦 依赖项

项目保持最小依赖：

```toml
dependencies = [
    "httpx>=0.25.0",      # HTTP客户端
    "fastmcp>=0.2.0",     # MCP框架
    "pydantic>=2.0.0",    # 数据验证
    "aiofiles>=23.0.0",   # 异步文件操作
]
```

## 🚀 部署方式

### 1. 开发环境
```bash
uv run python main.py
```

### 2. 生产环境（推荐）
```bash
# 构建wheel包
uv build

# 使用uvx运行（推荐）
uvx dist/codestandardmcp-1.0.0-py3-none-any.whl

# 或安装为工具
uv tool install dist/codestandardmcp-1.0.0-py3-none-any.whl
codestandardmcp
```

### 3. 容器化部署
```dockerfile
FROM python:3.11-slim
RUN pip install uv
COPY dist/codestandardmcp-1.0.0-py3-none-any.whl /app/
RUN uvx /app/codestandardmcp-1.0.0-py3-none-any.whl
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 使用uv进行开发和测试
4. 确保代码通过格式检查
5. 提交Pull Request

## 📄 许可证

MIT License - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [uv 文档](https://docs.astral.sh/uv/)
- [FastMCP 文档](https://github.com/jlowin/fastmcp)
- [MCP 协议规范](https://modelcontextprotocol.io/)
