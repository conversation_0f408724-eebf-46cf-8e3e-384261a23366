"""CodeStandardMCP - 编码规范即服务"""

__version__ = "1.0.0"
__author__ = "CodeStandardMCP"
__description__ = "编码规范服务工具，提供规范查询、合规检查和管理功能的MCP服务器"

from .config.settings import config
from .models.standard import StandardSelector, StandardDocument, StandardMetadata
from .services.standard_fetcher import standard_fetcher
from .services.standards_manager import standards_manager

__all__ = [
    "config",
    "StandardSelector",
    "StandardDocument", 
    "StandardMetadata",
    "standard_fetcher",
    "standards_manager",
]
