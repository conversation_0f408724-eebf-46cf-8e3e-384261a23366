"""
文件存储管理模块

提供文件读写、目录管理等基础存储功能
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import aiofiles

from ..config.settings import config

logger = logging.getLogger("CodeStandardMCP.FileManager")


class FileManager:
    """文件管理器类"""

    def __init__(self):
        self.encoding = "utf-8"

    async def read_json(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """异步读取JSON文件"""
        try:
            if not file_path.exists():
                logger.debug(f"File does not exist: {file_path}")
                return None

            async with aiofiles.open(file_path, "r", encoding=self.encoding) as f:
                content = await f.read()
                data = json.loads(content)
                logger.debug(f"Successfully read JSON file: {file_path}")
                return data

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error reading {file_path}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None

    async def write_json(
        self, file_path: Path, data: Dict[str, Any], ensure_dir: bool = True
    ) -> bool:
        """异步写入JSON文件"""
        try:
            if ensure_dir:
                file_path.parent.mkdir(parents=True, exist_ok=True)

            # 序列化数据
            json_content = json.dumps(data, ensure_ascii=False, indent=2, default=str)

            async with aiofiles.open(file_path, "w", encoding=self.encoding) as f:
                await f.write(json_content)

            logger.debug(f"Successfully wrote JSON file: {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error writing file {file_path}: {e}")
            return False

    async def read_text(self, file_path: Path) -> Optional[str]:
        """异步读取文本文件"""
        try:
            if not file_path.exists():
                logger.debug(f"File does not exist: {file_path}")
                return None

            async with aiofiles.open(file_path, "r", encoding=self.encoding) as f:
                content = await f.read()
                logger.debug(f"Successfully read text file: {file_path}")
                return content

        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return None

    async def write_text(
        self, file_path: Path, content: str, ensure_dir: bool = True
    ) -> bool:
        """异步写入文本文件"""
        try:
            if ensure_dir:
                file_path.parent.mkdir(parents=True, exist_ok=True)

            async with aiofiles.open(file_path, "w", encoding=self.encoding) as f:
                await f.write(content)

            logger.debug(f"Successfully wrote text file: {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error writing text file {file_path}: {e}")
            return False

    def get_file_size(self, file_path: Path) -> int:
        """获取文件大小（字节）"""
        try:
            if file_path.exists():
                return file_path.stat().st_size
            return 0
        except Exception as e:
            logger.error(f"Error getting file size {file_path}: {e}")
            return 0

    def get_file_mtime(self, file_path: Path) -> Optional[datetime]:
        """获取文件修改时间"""
        try:
            if file_path.exists():
                timestamp = file_path.stat().st_mtime
                return datetime.fromtimestamp(timestamp)
            return None
        except Exception as e:
            logger.error(f"Error getting file mtime {file_path}: {e}")
            return None

    def delete_file(self, file_path: Path) -> bool:
        """删除文件"""
        try:
            if file_path.exists():
                file_path.unlink()
                logger.debug(f"Successfully deleted file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return False

    def list_files(self, directory: Path, pattern: str = "*") -> list[Path]:
        """列出目录中的文件"""
        try:
            if not directory.exists():
                return []

            files = list(directory.glob(pattern))
            return [f for f in files if f.is_file()]

        except Exception as e:
            logger.error(f"Error listing files in {directory}: {e}")
            return []

    def ensure_directory(self, directory: Path) -> bool:
        """确保目录存在"""
        try:
            directory.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error creating directory {directory}: {e}")
            return False

    def get_directory_size(self, directory: Path) -> int:
        """获取目录总大小（字节）"""
        try:
            total_size = 0
            if directory.exists():
                for file_path in directory.rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
            return total_size
        except Exception as e:
            logger.error(f"Error calculating directory size {directory}: {e}")
            return 0

    def cleanup_empty_directories(self, root_directory: Path) -> int:
        """清理空目录"""
        try:
            removed_count = 0

            # 从最深层开始清理
            for directory in sorted(
                root_directory.rglob("*"), key=lambda p: len(p.parts), reverse=True
            ):
                if directory.is_dir() and directory != root_directory:
                    try:
                        # 尝试删除空目录
                        directory.rmdir()
                        removed_count += 1
                        logger.debug(f"Removed empty directory: {directory}")
                    except OSError:
                        # 目录不为空，跳过
                        pass

            return removed_count

        except Exception as e:
            logger.error(
                f"Error cleaning up empty directories in {root_directory}: {e}"
            )
            return 0

    async def backup_file(self, file_path: Path, backup_suffix: str = ".bak") -> bool:
        """备份文件"""
        try:
            if not file_path.exists():
                return False

            backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)

            # 读取原文件内容
            content = await self.read_text(file_path)
            if content is None:
                return False

            # 写入备份文件
            return await self.write_text(backup_path, content)

        except Exception as e:
            logger.error(f"Error backing up file {file_path}: {e}")
            return False

    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            if not file_path.exists():
                return {"exists": False}

            stat = file_path.stat()
            return {
                "exists": True,
                "size_bytes": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "is_file": file_path.is_file(),
                "is_dir": file_path.is_dir(),
                "name": file_path.name,
                "suffix": file_path.suffix,
            }

        except Exception as e:
            logger.error(f"Error getting file info {file_path}: {e}")
            return {"exists": False, "error": str(e)}


# 全局文件管理器实例
file_manager = FileManager()
